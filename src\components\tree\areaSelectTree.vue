<template>
  <kade-select-tree :value="value" :valueKey="valueKey" :dataTreeList="state.areaCheckTreeList" :list="state.list"
    :props="state.props" :multiple="multiple" @valueChange="valueChange" />
</template>
<script>
import { onMounted, reactive } from "vue";
import { getUserAreaPurview } from '@/applications/eccard-basic-data/api';
import { makeTree } from "@/utils/index.js";
import selectTree from "./selectTree";

export default {
  components: {
    "kade-select-tree": selectTree,
  },
  emits: ['valueChange'],
  props: {
    multiple: {
      types: Boolean,
      default: false,
    },
    value: {
      types: String || Array,
      default: "",
    },
    valueKey: {
      types: String,
      default: "id",
    },
  },
  setup(_props, context) {
    const state = reactive({
      areaCheckTreeList: [],
      list: [],
      props: {
        children: "children",
        label: "areaName",
        disabled: (data) => {
          return !data.checked
        }
      },
    });
    const queryAreaCheckList = () => {
      getUserAreaPurview().then((res) => {
        let arr = makeTree(res.data, "id", "areaParentId", "children");
        state.areaCheckTreeList = [...arr];
        state.list = res.data
      });
    };

    const valueChange = (val) => {
      context.emit("valueChange", val);
    };

    onMounted(() => {
      queryAreaCheckList();
    });

    return {
      state,
      valueChange,
    };
  },
};
</script>
<style lang="scss" scoped>

</style>