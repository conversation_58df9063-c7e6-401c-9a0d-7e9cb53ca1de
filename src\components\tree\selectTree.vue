<template>
  <el-dropdown trigger="click" ref="dropdown" placement="bottom-end" :max-height="300" :hide-on-click="false">
    <el-select ref="selectRef" :model-value="state.text" :multiple="multiple" :clearable="true"
      popper-class="select-name" class="dropdown-select-tree-input" @remove-tag="removeTag" @clear="handleClear()">
    </el-select>
    <template #dropdown>
      <el-dropdown-menu>
        <div class="dropdown-select-tree">
          <el-tree ref="treeRef" :data="dataTreeList" :props="props" :node-key="valueKey" :default-expand-all="false"
            :show-checkbox="multiple" :check-strictly="true" @node-expand="handleNodeExpand"
            @node-click="handleNodeClick" @check-change="handleCheckChange" />
        </div>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
<script>
// 下拉树组件
import { ElTree, ElSelect, ElDropdown, ElDropdownMenu } from "element-plus";
import { nextTick, onMounted, reactive, ref, watch } from "vue";
export default {
  components: {
    "el-tree": ElTree,
    ElSelect,
    "el-dropdown": ElDropdown,
    ElDropdownMenu,
  },
  emits: ['valueChange'],
  props: {
    dataTreeList: {
      types: Array,
      default: [],
    },
    list: {
      types: Array,
      default: [],
    },
    props: {
      types: Object,
      default: {},
    },
    multiple: {
      types: Boolean,
      default: false,
    },
    value: {
      types: Array || String,
      default: "",
    },
    valueKey: {
      types: String,
      default: "",
    },
  },
  setup(prop, context) {
    const treeRef = ref(null);
    const selectRef = ref(null);
    const state = reactive({
      text: prop.multiple ? [] : "",
      checkedList: [], //选中的节点对象
      noDisabledList: [],
    });

    //监听绑定值是否为空
    watch(
      () => prop.value,
      (val) => {
        console.log(1);
        if (prop.multiple) {
          if (val.length) {
            nextTick(() => {
              treeRef.value.setCheckedKeys(val);
            });
          } else {
            treeRef.value.setCheckedKeys([]);
            state.text = [];
            state.checkedList = [];
          }
        } else {
          console.log(prop.value)
          if (val || val === 0) {
            nextTick(() => {
              console.log(treeRef.value.getNode(val));
              state.text = treeRef.value.getNode(val) && treeRef.value.getNode(val).data[prop.props.label];
            });
          } else {
            treeRef.value.setCheckedKeys([]);
            state.text = "";
          }
        }
      },
      { deep: true }
    );

    //监听初次进入数据赋值绑定
    watch(
      () => prop.dataTreeList,
      () => {

        if (prop.multiple) {
          nextTick(() => {
            if (prop.value) {
              treeRef.value.setCheckedKeys(prop.value);
            }
          });
        } else {
          nextTick(() => {

            if (prop.value) {
              treeRef.value.setCheckedKeys([prop.value]);
              state.text = treeRef.value.getNode(prop.value).data[prop.props.label];
            }
          });
        }
        nextTick(() => {
          state.noDisabledList = prop.list.filter(item => item.checked).map(item => String(item[prop.valueKey]))
          console.log(state.noDisabledList);
          let arr = document.querySelectorAll(".el-tree-node")
          console.log(arr);
          arr.forEach(item => {
            if (!item.style.color) {
              if (state.noDisabledList.includes(item.dataset.key)) {
                item.style.color = "#333"
              } else {
                item.style.color = "#ccc"
              }
            }
          })
        })
      }
    );

    const handleNodeExpand = (data) => {
      console.log(data);
      let dataList = data.children.filter(item => item.checked).map(item => String(item[prop.valueKey]))
      nextTick(() => {
        let arr = document.querySelectorAll(".el-tree-node")
        arr.forEach(item => {
          if (!item.style.color) {
            if (dataList.includes(item.dataset.key)) {
              item.style.color = "#333"
            } else {
              item.style.color = "#ccc"
            }
          }

        })
      })


    }
    //点击节点触发
    const handleNodeClick = (data) => {
      if (!data.checked) return
      //非复选才执行
      if (!prop.multiple) {
        console.log(data);
        state.text = data[prop.props.label];
        context.emit("valueChange", data);
      }
    };
    //选择节点触发
    const handleCheckChange = (data, isChecked) => {
      if (prop.multiple) {
        console.log(data, isChecked);
        if (isChecked) {
          state.checkedList.push(data);
        } else {
          state.checkedList = state.checkedList.filter(
            (item) => item[prop.props.label] !== data[prop.props.label]
          );
        }
        state.text = state.checkedList.map((item) => item[prop.props.label]);
        context.emit(
          "valueChange",
          state.checkedList.map((item) => item[prop.valueKey])
        );
      }
    };

    //多选情况下删除每一项
    const removeTag = (val) => {
      state.checkedList = state.checkedList.filter(
        (item) => item[prop.props.label] !== val
      );
      state.text = state.text.filter((item) => item !== val);

      //删除每一项时清除树选中
      treeRef.value.setCheckedKeys(
        state.checkedList.map((item) => item[prop.valueKey])
      );

      context.emit(
        "valueChange",
        state.checkedList.map((item) => item[prop.valueKey])
      );
    };

    //清除选项
    const handleClear = () => {
      if (prop.multiple) {
        state.checkedList = [];
        state.text = [];
        treeRef.value.setCheckedKeys([]);
        context.emit("valueChange", []);
      } else {
        state.text = "";
        context.emit("valueChange", {});
      }
    };

    onMounted(() => {
      //清除el-select默认下拉框
      let selectOptionDom = document.querySelector(".select-name");
      selectOptionDom.remove();
      let selectDom = document
        .querySelector(".dropdown-select-tree-input")
        .getElementsByClassName("el-input__inner");
      selectDom[0].style.paddingLeft = "15px";
    });

    return {
      treeRef,
      selectRef,
      state,
      handleNodeExpand,
      handleNodeClick,
      handleCheckChange,
      handleClear,
      removeTag,
    };
  },
};
</script>
<style lang="scss" scoped>
.dropdown-select-tree {
  padding: 0 20px;
  box-sizing: border-box;
  width: 400px;

  .el-tree {
    width: auto;
    display: inline-block;
    min-width: 100%;
  }
}

:deep(.el-scrollbar__wrap) {
  overflow-x: auto;

}

.dropdown-select-tree-input {
  width: 100%;

  &:hover {
    .el-icon-circle-close {
      display: inline-block;
    }
  }
}

.select-name {
  display: none;
}
</style>