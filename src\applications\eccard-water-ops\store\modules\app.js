import { markRaw, defineAsyncComponent } from "vue";
import { getDictionary } from "@/applications/unified_portal/api";
import {
  actions as mixinActions,
  state as mixinState,
  getters as mixinGettgers
} from "@/utils/tab.mixin";
const dictionary = localStorage.getItem(`kade_cache_dictionary`);
const state = {
  ...mixinState,
  dictionary: dictionary ? JSON.parse(dictionary) : [],
  applyTypes: [],
  componentMap: {
    home: markRaw(
      defineAsyncComponent(() => import("../../views/home/<USER>"))
    ),
    warningList: markRaw(
      defineAsyncComponent(() => import("../../views/warningList/index.vue"))
    ),
    warningConfig: markRaw(
      defineAsyncComponent(() => import("../../views/warningConfig/index.vue"))
    ),
    cost: markRaw(
      defineAsyncComponent(() => import("../../views/cost/index.vue"))
    ),
    auth: markRaw(
      defineAsyncComponent(() => import("../../views/auth/index.vue"))
    ),
    deviceReservation: markRaw(
      defineAsyncComponent(() => import("../../views/deviceReservation/index.vue"))
    )
  },
  customMenus: []
};
const mutations = {
  updateState(state, { key, payload }) {
    state[key] = payload;
  }
};
const actions = {
  ...mixinActions,
  async loadDictionary({ commit }) {
    const { data } = await getDictionary();
    localStorage.setItem(`kade_cache_dictionary`, JSON.stringify(data));
    commit("updateState", { key: "dictionary", payload: data });
  }
};

const getters = {
  ...mixinGettgers
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
