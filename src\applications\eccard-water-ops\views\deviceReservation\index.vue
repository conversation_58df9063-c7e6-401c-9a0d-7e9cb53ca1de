<template>
  <div class="personnelInformation">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="handleSearch" @reset="handleReset">
        <el-form inline size="small" label-width="100px">
          <el-form-item label="所属区域">
            <kade-area-select-tree style="width: 200px" :value="state.form.areaId" valueKey="id" :multiple="false"
              @valueChange="(val) => (state.form.areaId = val.id)" />
          </el-form-item>
          <el-form-item label="预约时间段">
            <div style="display: flex; gap: 5px; align-items: center;">
              <el-time-select
                placeholder="开始时间"
                v-model="state.form.startTime"
                :picker-options="{
                  start: '00:00',
                  step: '00:15',
                  end: '23:45'
                }"
                style="width: 120px;">
              </el-time-select>
              <span style="color: #909399;">至</span>
              <el-time-select
                placeholder="结束时间"
                v-model="state.form.endTime"
                :picker-options="{
                  start: '00:15',
                  step: '00:15',
                  end: '23:59',
                  minTime: state.form.startTime
                }"
                style="width: 120px;">
              </el-time-select>
            </div>
          </el-form-item>
          <el-form-item label="是否启用">
            <el-select v-model="state.form.status" placeholder="请选择" clearable style="width: 120px">
              <el-option label="启用" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="设备信息">
        <template #extra>
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        </template>
        <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh"
          highlight-current-row border stripe>
          <el-table-column show-overflow-tooltip label="区域名称" prop="deviceType" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="区域标签" prop="deviceNo" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="上级区域" prop="deviceName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="区域地址" prop="deviceModel" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="预约时间段" prop="areaName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="是否启用" prop="deviceStatus" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.deviceStatus === 1 ? 'success' : 'danger'">
                {{ scope.row.deviceStatus === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button size="mini" type="text" @click="handleBindFood(scope.row)">编辑</el-button>
              <el-button size="mini" type="text" @click="handleDetails(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize"
            layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
            @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="state.dialogTitle" v-model="state.dialogVisible" width="600px" :close-on-click-modal="false">
      <el-form :model="state.formData" :rules="state.rules" ref="formRef" label-width="120px">
        <el-form-item label="所属区域" prop="areaId">
          <kade-area-select-tree style="width: 100%" :value="state.formData.areaId" valueKey="id" :multiple="false"
            @valueChange="(val) => (state.formData.areaId = val.id)" />
        </el-form-item>
       <!--  <el-form-item label="区域名称">
          <el-input
            v-model="state.formData.areaName"
            placeholder="区域名称"
            :disabled="state.dialogTitle === '编辑设备预约'"
            style="width: 100%;">
          </el-input>
        </el-form-item>
        <el-form-item label="区域标签">
          <el-input
            v-model="state.formData.areaTag"
            placeholder="区域标签"
            :disabled="state.dialogTitle === '编辑设备预约'"
            style="width: 100%;">
          </el-input>
        </el-form-item>
        <el-form-item label="上级区域">
          <el-input
            v-model="state.formData.parentArea"
            placeholder="上级区域"
            :disabled="state.dialogTitle === '编辑设备预约'"
            style="width: 100%;">
          </el-input>
        </el-form-item>
        <el-form-item label="区域地址">
          <el-input
            v-model="state.formData.areaAddress"
            placeholder="区域地址"
            :disabled="state.dialogTitle === '编辑设备预约'"
            style="width: 100%;">
          </el-input>
        </el-form-item> -->
        <el-form-item label="预约时间段">
          <div style="display: flex; gap: 10px; align-items: center; width: 100%;">
            <el-form-item prop="startTime" style="flex: 1; margin-bottom: 0;">
              <el-time-select
                placeholder="起始时间"
                v-model="state.formData.startTime"
                :picker-options="{
                  start: '00:00',
                  step: '00:15',
                  end: '23:45'
                }"
                style="width: 100%;">
              </el-time-select>
            </el-form-item>
            <span style="color: #909399; margin: 0 5px;">至</span>
            <el-form-item prop="endTime" style="flex: 1; margin-bottom: 0;">
              <el-time-select
                placeholder="结束时间"
                v-model="state.formData.endTime"
                :picker-options="{
                  start: '00:15',
                  step: '00:15',
                  end: '23:59',
                  minTime: state.formData.startTime
                }"
                style="width: 100%;">
              </el-time-select>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="是否启用" prop="status">
          <el-radio-group v-model="state.formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElForm,
  ElFormItem,
  ElPagination,
  ElButton,
  ElDialog,
  ElTimeSelect,
  ElRadioGroup,
  ElRadio,
  ElSelect,
  ElOption,
  ElTag,
  ElInput,
} from "element-plus";


import areaSelectTree from "@/components/tree/areaSelectTree.vue";
export default {
  components: {
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-pagination": ElPagination,
    "el-button": ElButton,
    "el-dialog": ElDialog,
    "el-time-select": ElTimeSelect,
    "el-radio-group": ElRadioGroup,
    "el-radio": ElRadio,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-tag": ElTag,
    "el-input": ElInput,
    "kade-area-select-tree": areaSelectTree,
  },
};
</script>
<script setup>
import { reactive, onMounted, ref } from "vue";
import {
  devicePage,
} from "@/applications/eccard-iot/api";

// 表单引用
const formRef = ref(null);

const state = reactive({
  isBind: false,
  isDetails: false,
  loading: false,
  form: {
    pageSize: 10,
    pageNum: 1,
    areaId: '',
    startTime: '',
    endTime: '',
    status: ''
  },
  dataList: [],
  total: 0,
  rowData: {},
  // 弹窗相关
  dialogVisible: false,
  dialogTitle: '新增设备预约',
  formData: {
    areaId: '',
    areaName: '',
    areaTag: '',
    parentArea: '',
    areaAddress: '',
    startTime: '00:00',
    endTime: '23:59',
    status: 1
  },
  rules: {
    areaId: [
      { required: true, message: '请选择所属区域', trigger: 'change' }
    ],
    startTime: [
      { required: true, message: '请选择开始时间', trigger: 'change' }
    ],
    endTime: [
      { required: true, message: '请选择结束时间', trigger: 'change' }
    ],
    status: [
      { required: true, message: '请选择是否启用', trigger: 'change' }
    ]
  }
});

const getList = async () => {
  state.loading = true;
  for (let key in state.form) {
    if (!state.form[key]) {
      delete state.form[key];
    }
  }

  // Mock数据
  const mockData = {
    list: [
      {
        id: 1,
        deviceType: '华南师范大学',
        deviceNo: '教学区',
        deviceName: '石牌校区',
        deviceModel: '广州市天河区中山大道西55号',
        areaName: '08:00-18:00',
        deviceStatus: 1,
        startTime: '08:00',
        endTime: '18:00',
        status: 1
      },
      {
        id: 2,
        deviceType: '华南师范大学',
        deviceNo: '宿舍区',
        deviceName: '石牌校区',
        deviceModel: '广州市天河区中山大道西55号',
        areaName: '06:00-23:00',
        deviceStatus: 1,
        startTime: '06:00',
        endTime: '23:00',
        status: 1
      },
      {
        id: 3,
        deviceType: '华南师范大学',
        deviceNo: '图书馆',
        deviceName: '大学城校区',
        deviceModel: '广州市番禺区大学城外环西路378号',
        areaName: '07:30-22:30',
        deviceStatus: 0,
        startTime: '07:30',
        endTime: '22:30',
        status: 0
      },
      {
        id: 4,
        deviceType: '华南师范大学',
        deviceNo: '体育馆',
        deviceName: '大学城校区',
        deviceModel: '广州市番禺区大学城外环西路378号',
        areaName: '06:30-21:30',
        deviceStatus: 1,
        startTime: '06:30',
        endTime: '21:30',
        status: 1
      },
      {
        id: 5,
        deviceType: '华南师范大学',
        deviceNo: '实验楼',
        deviceName: '南海校区',
        deviceModel: '佛山市南海区狮山镇南海软件科技园',
        areaName: '08:30-17:30',
        deviceStatus: 0,
        startTime: '08:30',
        endTime: '17:30',
        status: 0
      }
    ],
    total: 5
  };

  // 模拟API调用延迟
  setTimeout(() => {
    state.loading = false;
    state.dataList = mockData.list;
    state.total = mockData.total;
  }, 500);

  // 注释掉真实API调用，使用mock数据
  // let { data } = await devicePage(state.form);
  // state.loading = false;
  // state.dataList = data.list;
  // state.total = data.total;
};

// 新增按钮点击
const handleAdd = () => {
  state.dialogTitle = '新增设备预约';
  state.formData = {
    areaId: '',
    areaName: '',
    areaTag: '',
    parentArea: '',
    areaAddress: '',
    startTime: '00:00',
    endTime: '23:59',
    status: 1
  };
  state.dialogVisible = true;
}

// 编辑按钮点击
const handleBindFood = (row) => {
  state.dialogTitle = '编辑设备预约';
  state.formData = {
    areaId: row.areaId || '',
    areaName: row.deviceType || '',      // 区域名称对应表格的区域名称
    areaTag: row.deviceNo || '',         // 区域标签对应表格的区域标签
    parentArea: row.deviceName || '',    // 上级区域对应表格的上级区域
    areaAddress: row.deviceModel || '',  // 区域地址对应表格的区域地址
    startTime: row.startTime || '00:00',
    endTime: row.endTime || '23:59',
    status: row.status || 1
  };
  state.rowData = row;
  state.dialogVisible = true;
}

// 删除按钮点击
const handleDetails = (row) => {
  console.log('删除', row);
  // 这里可以添加删除确认逻辑
}

// 取消按钮
const handleCancel = () => {
  state.dialogVisible = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

// 提交表单
const handleSubmit = () => {
  if (formRef.value) {
    formRef.value.validate((valid) => {
      if (valid) {
        console.log('提交数据:', state.formData);
        // 这里可以调用API保存数据
        state.dialogVisible = false;
        getList(); // 刷新列表
      } else {
        console.log('表单验证失败');
      }
    });
  }
}

const handleSearch = () => {
  getList();
}

const handleReset = () => {
  state.form = {
    pageSize: 10,
    pageNum: 1,
    areaId: '',
    startTime: '',
    endTime: '',
    status: ''
  }
  getList();
}

const handlePageChange = val => {
  state.form.pageNum = val
  getList();
}

const handleSizeChange = val => {
  state.form.pageNum = 1
  state.form.pageSize = val
  getList();
}

onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  margin-top: 20px;
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>
