<template>
  <div class="personnelInformation">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="handleSearch" @reset="handleReset">
        <el-form inline size="small" label-width="100px">
          <el-form-item label="所属区域">
            <kade-area-select-tree style="width: 100%" :value="state.form.areaId" valueKey="id" :multiple="false"
              @valueChange="(val) => (state.form.areaId = val.id)" />
          </el-form-item>

        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="设备信息">
        <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh"
          highlight-current-row border stripe>
          <el-table-column show-overflow-tooltip label="区域名称" prop="deviceType" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="区域标签" prop="deviceNo" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="上级区域" prop="deviceName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="区域地址" prop="deviceModel" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="预约时间段" prop="areaName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="否启" prop="deviceStatus" align="center">
            <template #default="scope">
              {{ dictionaryFilter(scope.row.deviceStatus) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button size="mini" type="text" @click="handleBindFood(scope.row)">编辑</el-button>
              <el-button size="mini" type="text" @click="handleDetails(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize"
            layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
            @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElForm,
  ElFormItem,
  ElPagination,
  ElButton,
} from "element-plus";
import { reactive, onMounted } from "vue";
import {
  devicePage,
} from "@/applications/eccard-iot/api";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";
export default {
  components: {
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-pagination": ElPagination,
    "el-button": ElButton,
    "kade-area-select-tree": areaSelectTree,
  },
  setup() {
    const state = reactive({
      isBind: false,
      isDetails: false,
      form: {
        pageSize: 10,
        pageNum: 1,
      },
      dataList: [],
      total: 0,
      rowData: {},
    });
    const getList = async () => {
      state.loading = true;
      for (let key in state.form) {
        if (!state.form[key]) {
          delete state.form[key];
        }
      }
      let { data } = await devicePage(state.form);
      state.loading = false;
      state.dataList = data.list;
      state.total = data.total;
    };

    const handleBindFood = () => {
      console.log('编辑');

    }
    const handleDetails = () => {
      console.log('编辑');
    }
    const handleSearch = () => {
      getList();
    }
    const handleReset = () => {
      state.form = {
        pageSize: 10,
        pageNum: 1,
      }
    }
    const handlePageChange = val => {
      state.form.pageNum = val
      getList();
    }
    const handleSizeChange = val => {
      state.form.pageNum = 1
      state.form.pageSize = val
      getList();
    }
    onMounted(() => {
      getList();
    });
    return {
      state,
      handleBindFood,
      handleDetails,
      handleSearch,
      handleReset,
      handlePageChange,
      handleSizeChange,
    };
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  margin-top: 20px;
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>
